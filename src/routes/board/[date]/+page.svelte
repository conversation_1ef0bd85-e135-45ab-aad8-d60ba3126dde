<script lang="ts">
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	// Format date for display
	function formatDate(dateStr: string): string {
		try {
			const date = new Date(dateStr + 'T00:00:00.000Z');
			return date.toLocaleDateString('en-US', {
				weekday: 'long',
				year: 'numeric',
				month: 'long',
				day: 'numeric'
			});
		} catch {
			return dateStr;
		}
	}

	// Calculate total per-round score for comparison
	let perRoundTotal = $derived(
		data.result?.perRound.reduce((sum, round) => sum + round.score, 0) || 0
	);
	let improvement = $derived(data.result ? data.result.total - perRoundTotal : 0);
</script>

<svelte:head>
	<title>Letters Board - {data.date}</title>
	<meta name="description" content="LettersBot solver results for {formatDate(data.date)}" />
</svelte:head>

<div class="min-h-screen bg-gray-50 py-8">
	<div class="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
		<!-- Header -->
		<div class="mb-8 text-center">
			<h1 class="mb-2 text-3xl font-bold text-gray-900">Letters Board Results</h1>
			<p class="text-lg text-gray-600">
				{formatDate(data.date)}
			</p>
		</div>

		{#if data.error}
			<!-- Error State -->
			<div class="rounded-lg border border-red-200 bg-red-50 p-6 text-center">
				<div class="mb-2 text-red-600">
					<svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
						/>
					</svg>
				</div>
				<h2 class="mb-2 text-xl font-semibold text-red-800">No Results Available</h2>
				<p class="text-red-700">{data.error}</p>
				<div class="mt-4">
					<a
						href="/"
						class="inline-flex items-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-none"
					>
						← Back to Home
					</a>
				</div>
			</div>
		{:else if data.result}
			<!-- Results Display -->
			<div class="space-y-6">
				<!-- Summary Card -->
				<div class="rounded-lg bg-white p-6 shadow-md">
					<div class="grid grid-cols-1 gap-6 md:grid-cols-3">
						<div class="text-center">
							<div class="text-3xl font-bold text-blue-600">{data.result.total}</div>
							<div class="text-sm text-gray-600">Optimal Total</div>
						</div>
						<div class="text-center">
							<div class="text-3xl font-bold text-green-600">{data.result.words.length}</div>
							<div class="text-sm text-gray-600">Words Played</div>
						</div>
						<div class="text-center">
							<div
								class="text-3xl font-bold {improvement >= 0 ? 'text-green-600' : 'text-red-600'}"
							>
								{improvement >= 0 ? '+' : ''}{improvement}
							</div>
							<div class="text-sm text-gray-600">vs. Greedy</div>
						</div>
					</div>
				</div>

				<!-- Optimal Solution -->
				<div class="rounded-lg bg-white p-6 shadow-md">
					<h2 class="mb-4 text-xl font-semibold text-gray-900">Optimal Solution</h2>
					<div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
						{#each data.result.words as word, index (word)}
							<div class="rounded-lg border border-blue-200 bg-blue-50 p-3 text-center">
								<div class="text-sm font-medium text-blue-600">Move {index + 1}</div>
								<div class="text-lg font-bold text-blue-900">{word.toUpperCase()}</div>
							</div>
						{/each}
					</div>
				</div>

				<!-- Per-Round Comparison -->
				<div class="rounded-lg bg-white p-6 shadow-md">
					<h2 class="mb-4 text-xl font-semibold text-gray-900">Greedy vs. Optimal Comparison</h2>
					<div class="overflow-x-auto">
						<table class="min-w-full divide-y divide-gray-200">
							<thead class="bg-gray-50">
								<tr>
									<th
										class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
										>Round</th
									>
									<th
										class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
										>Greedy Word</th
									>
									<th
										class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
										>Greedy Score</th
									>
									<th
										class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
										>Optimal Word</th
									>
									<th
										class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
										>Details</th
									>
								</tr>
							</thead>
							<tbody class="divide-y divide-gray-200 bg-white">
								{#each data.result.perRound as round, index (round.word)}
									<tr class="hover:bg-gray-50">
										<td class="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900">
											{index + 1}
										</td>
										<td class="px-6 py-4 font-mono text-sm whitespace-nowrap text-gray-900">
											{round.word.toUpperCase()}
										</td>
										<td class="px-6 py-4 text-sm whitespace-nowrap text-gray-900">
											{round.score}
										</td>
										<td class="px-6 py-4 font-mono text-sm whitespace-nowrap text-gray-900">
											{data.result.words[index]?.toUpperCase() || '-'}
										</td>
										<td class="px-6 py-4 text-sm whitespace-nowrap text-gray-900">
											{#if data.result.enhancedMoves && data.result.enhancedMoves[index]}
												<details class="cursor-pointer">
													<summary class="text-blue-600 hover:text-blue-800">View Board</summary>
													<div class="mt-2 rounded border bg-gray-50 p-2">
														<div class="mb-2 text-xs text-gray-600">
															Positions: {data.result.enhancedMoves[index].positions
																.map(([r, c]) => `[${r},${c}]`)
																.join(', ')}
														</div>
														<div class="grid w-fit grid-cols-5 gap-1">
															{#if data.result.enhancedMoves[index].boardBefore}
																{#each data.result.enhancedMoves[index].boardBefore as row, rowIndex (rowIndex)}
																	{#each row as tile, colIndex (`${rowIndex}-${colIndex}`)}
																		{@const isSelected = data.result.enhancedMoves[
																			index
																		].positions.some(([r, c]) => r === rowIndex && c === colIndex)}
																		<div
																			class="flex h-8 w-8 items-center justify-center border border-gray-300 font-mono text-xs {isSelected
																				? 'border-blue-500 bg-blue-200 font-bold'
																				: 'bg-white'}"
																			title="{tile.letter} at [{rowIndex},{colIndex}]{tile.letterMult >
																			1
																				? ` L${tile.letterMult}x`
																				: ''}{tile.wordMult > 1 ? ` W${tile.wordMult}x` : ''}"
																		>
																			{tile.letter}
																		</div>
																	{/each}
																{/each}
															{:else}
																<div class="text-xs text-gray-500">Board state not available</div>
															{/if}
														</div>
													</div>
												</details>
											{:else}
												<span class="text-xs text-gray-400">No details</span>
											{/if}
										</td>
									</tr>
								{/each}
							</tbody>
						</table>
					</div>
					<div class="mt-4 text-sm text-gray-600">
						<p><strong>Greedy Total:</strong> {perRoundTotal} points</p>
						<p><strong>Optimal Total:</strong> {data.result.total} points</p>
						<p>
							<strong>Improvement:</strong>
							{improvement >= 0 ? '+' : ''}{improvement} points ({improvement >= 0 ? '+' : ''}{(
								(improvement / perRoundTotal) *
								100
							).toFixed(1)}%)
						</p>
					</div>
				</div>

				<!-- Navigation -->
				<div class="flex justify-center space-x-4">
					<a
						href="/"
						class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none"
					>
						← Back to Home
					</a>
					<button
						onclick={() => {
							if (navigator.share) {
								navigator.share({ title: 'Letters Board Results', url: window.location.href });
							} else {
								navigator.clipboard.writeText(window.location.href);
							}
						}}
						class="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none"
					>
						Share Results
					</button>
				</div>
			</div>
		{:else}
			<!-- Loading State -->
			<div class="rounded-lg bg-white p-6 text-center shadow-md">
				<div
					class="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"
				></div>
				<p class="text-gray-600">Loading board results...</p>
			</div>
		{/if}
	</div>
</div>

<style>
	/* Additional custom styles if needed */
</style>
